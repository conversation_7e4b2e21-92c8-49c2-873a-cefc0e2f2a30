use crate::bootnode::HTTP_CLIENT;
use crate::webapi::AppState;
use actix_web::{delete, get, post, put, web, HttpMessage, HttpRequest, HttpResponse, Result};
use dpn_core::types::api::ErrorWrapper;
use dpn_core::types::{auth::UserClaims, connection::PrioritizedIPLevel};
use log::error;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::{net::IpAddr, sync::Arc};
use utoipa::ToSchema;
use log::{info,warn};
use sqlx::types::chrono::Utc;
use std::time::Instant;
// use crate::bootnode::utils::get_geo_from_ip_address;
// use regex::Regex;
// use std::collections::HashMap;
// use dpn_core::types::geo::Geo;


struct Defer<F: FnOnce()> {
    f: Option<F>
}

impl<F: FnOnce()> Drop for Defer<F> {
    fn drop(&mut self) {
        if let Some(f) = self.take_f() {
            f()
        }
    }
}

impl<F: FnOnce()> Defer<F> {
    fn new(f: F) -> Defer<F> {
        Defer { f: Some(f) }
    }

    fn take_f(&mut self) -> Option<F> {
        self.f.take()
    }
}

pub fn init_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(update_proxy_acc)
        .service(delete_proxy_acc)
        .service(create_proxy_acc)
        .service(get_proxy_acc_data)
        .service(get_client_overview)
        .service(get_client_usage_history)
        .service(refresh_proxy)
        // Algo proxies integration
        .service(create_algo_proxy)
        .service(delete_algo_proxy)
        .service(get_peer_stats_by_country)
        .service(force_stop_peer)
        .service(health_check_peer);
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct AlgoProxyRequest {
    pub country_iso_code: String,
    pub algo_id_session: String,
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct AlgoDeleteProxyRequest {
    pub algo_id_session: String,
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct AlgoProxyResponse {
    pub country_iso_code: String,
    pub algo_session_id: String,
    pub username: String, // use default username
    pub password: String, // this is the session hash of u2dpn
    pub proxy_ip: String,
    pub proxy_port: String,
    pub provider_public_ip_address: String,
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct AlgoProxyInfo {
    pub algo_id_session: String,
    pub username: String,
    pub password: String,
    pub proxy_ip: String,
    pub proxy_port: String,
    pub country_geoname_id: i64,
    pub city_geo_name_id: i64,
    pub ttl: u32,
    pub peer_ipu32: String, // this field using flag to monitor peer ip change or disconnect
    pub peer_ip_addr: String, // provider ip v4 address
}
#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct ClientOverview {
    pub user_addr: String,
    pub balance: i64,
    pub active_proxies: i64,
    pub inactive_proxies: i64,
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct ProxyAccUpdate {
    pub proxy_acc_id: String,
    pub ip_rotation_period: u32,
    pub whitelisted_ip: Option<String>,
    pub rate_per_kb: u32,
    pub rate_per_second: u32,
    pub country_geoname_id: u32,
    pub city_geoname_id: Option<u32>,
    pub prioritized_ip: Option<String>,
    pub prioritized_ip_level: Option<PrioritizedIPLevel>,
}

#[derive(Debug, Clone, Deserialize, ToSchema)]
pub struct ProxyAccDelete {
    pub proxy_acc_id: String,
}

#[derive(Debug, Clone, Deserialize, ToSchema)]
pub struct ProxyAccInfo {
    pub rate_per_kb: u32,
    pub rate_per_second: u32,
    pub ip_rotation_period: i64,
    pub country_geoname_id: u32,
    pub whitelisted_ip: Option<String>,
    pub prioritized_ip: Option<String>,
    pub prioritized_ip_level: Option<PrioritizedIPLevel>,
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct PeerByCountryStats {
    iso_code: String,
    name: String,
    geoname_id: String,
    total_active_ip: u32,
    // total_active_user: u32,
    // total_active_bandwidth: u64,
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct OnlinePeerByGeo {
    pub countries: Vec<PeerByCountryStats>,
}

#[utoipa::path(
    post,
    path = "/clients/algo/proxies",
    tag = "Algo",
    request_body(content = AlgoProxyRequest, description = "Create Algo Proxy"),
    responses(
        (status=200, description= "Create Algo Proxy successfully!", body=AlgoProxyResponse),
        (status=400, description= "Create Algo Proxy failed", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[post("algo/proxies")]
pub async fn create_algo_proxy(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<AlgoProxyRequest>,
) -> Result<HttpResponse> {

    let connection_svc = app_state.connection_svc.clone();
    let algo_proxy_req = body.clone();
    info!("Create new algo session with request: {:?}", algo_proxy_req);
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let masternode_map_lock = app_state.bootnode_svc.clone().get_masternode_map().await;
    let algo_proxy_response = connection_svc
        .create_algo_proxy(user_addr, algo_proxy_req, masternode_map_lock)
        .await;
    match algo_proxy_response {
        Ok(algo_proxy_response) => Ok(HttpResponse::Ok().json(algo_proxy_response)),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::BAD_REQUEST,
            &format!("cannot create algo proxy err={}", e),
        )
        .build()),
    }
}

#[utoipa::path(
    get,
    path = "/clients/algo/proxies/online-provider-by-location",
    tag = "Algo",
    responses(
        (status=200, description= "Get online provider by location successfully!", body=OnlinePeerByGeo),
        (status=500, description= "Server error", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("algo/proxies/online-provider-by-location")]
pub async fn get_peer_stats_by_country(
    _app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    // hard code curl to end point
    // TODO: fix this hardcode
    let response = match HTTP_CLIENT.get("https://stats.u2dpn.xyz/stats/online-providers-by-locations").send().await {
        Ok(response) => response,
        Err(e) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                &format!("Failed to get online peers: {}", e),
            ).build());
        }
    };
    let result = match response.json::<OnlinePeerByGeo>().await {
        Ok(result) => result,
        Err(e) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                &format!("Failed to parse response body: {}", e),
            ).build());
        }
    };
    return Ok(HttpResponse::Ok().json(result));
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct ForceStopPeerRequest {
    pub peer_ip: String,
}

#[utoipa::path(
    post,
    path = "/clients/algo/proxies/force-stop-peer",
    tag = "Algo",
    request_body(content = ForceStopPeerRequest, description = "Force stop peer"),
    responses(
        (status=200, description= "Force stop peer successfully!"),
        (status=400, description= "Force stop peer failed", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[post("algo/proxies/force-stop-peer")]
pub async fn force_stop_peer(
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<ForceStopPeerRequest>,
) -> Result<HttpResponse> {
    let force_stop_peer_request = body.clone();
    let bootnode_svc = app_state.bootnode_svc.clone();
    let result = bootnode_svc.force_stop_peer(force_stop_peer_request.peer_ip).await;
    match result {
        Ok(_) => Ok(HttpResponse::Ok().into()),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::INTERNAL_SERVER_ERROR,
            &format!("cannot force stop peer err={}", e),

        )
        .build()),
    }
}

#[utoipa::path(
    delete,
    path = "/clients/algo/proxies/{algo_id_session}",
    tag = "Algo",
    responses(
        (status=200, description= "Delete Algo Proxy successfully!"),
        (status=500, description= "Server error", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[delete("algo/proxies/{algo_id_session}")]
pub async fn delete_algo_proxy(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    algo_id_session: web::Path<String>,
) -> Result<HttpResponse> {

    let connection_svc = app_state.connection_svc.clone();
    let algo_id_session = algo_id_session.into_inner();
    info!("Delete session: {:?}", algo_id_session);
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    match connection_svc
        .delete_algo_proxy(&user_addr, &algo_id_session)
        .await
    {
        Ok(algo_id_session) => Ok(HttpResponse::Ok().json(algo_id_session)),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::INTERNAL_SERVER_ERROR,
            &format!("cannot delete algo proxy err={}", e),
        )
        .build()),
    }
}

#[utoipa::path(
    put,
    path = "/clients/proxies",
    tag = "Clients",
    request_body(content = ProxyAccUpdate, description = "Update Proxy Acc"),
    responses(
        (status=200, description= "Update proxy accounts successfully!"),
        (status=400, description= "Bad request", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[put("/proxies")]
pub async fn update_proxy_acc(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<ProxyAccUpdate>,
) -> Result<HttpResponse> {
    let user_svc = app_state.user_svc.clone();
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let mut proxy_acc_update: ProxyAccUpdate = body.clone();

    if let Some(addr) = proxy_acc_update.whitelisted_ip.clone() {
        if let Err(_) = IpAddr::parse_ascii(addr.as_bytes()) {
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                &format!("ip not a address: {}", &addr),
            )
            .build());
        }
    }

    match proxy_acc_update.prioritized_ip.clone() {
        Some(prioritized_ip) => {
            if let Err(_) = IpAddr::parse_ascii(prioritized_ip.as_bytes()) {
                return Ok(ErrorWrapper::builder(
                    StatusCode::BAD_REQUEST,
                    &format!("prioritized_ip not a address: {}", &prioritized_ip),
                )
                .build());
            }
            if proxy_acc_update.prioritized_ip_level.is_none() {
                return Ok(ErrorWrapper::builder(
                    StatusCode::BAD_REQUEST,
                    r"#prioritized_ip_level isn't specified#",
                )
                .build());
            }
        }
        None => {
            // set level to null if prioritized ip is not specified
            proxy_acc_update.prioritized_ip_level = None;
        }
    }

    match user_svc
        .update_proxy_account(user_addr, proxy_acc_update.clone())
        .await
    {
        Ok(_) => Ok(HttpResponse::Ok().into()),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::BAD_REQUEST,
            &format!("cannot update proxy acc data err= {}", e),
        )
        .build()),
    }
}

#[utoipa::path(
    delete,
    path = "/clients/proxies",
    tag = "Clients",
    request_body(content = ProxyAccDelete, description = "Delete Proxy Acc"),
    responses(
        (status=200, description= "Delete Proxy Acc successfully!"),
        (status=400, description= "Delete Proxy Acc failed", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[delete("/proxies")]
pub async fn delete_proxy_acc(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<ProxyAccDelete>,
) -> Result<HttpResponse> {
    let user_svc = app_state.user_svc.clone();
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    match user_svc
        .delete_proxy_account(user_addr, body.0.proxy_acc_id)
        .await
    {
        Ok(_) => Ok(HttpResponse::Ok().into()),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::BAD_REQUEST,
            &format!("cannot delete proxy acc data err={}", e),
        )
        .build()),
    }
}

#[utoipa::path(
    post,
    path = "/clients/proxies",
    tag = "Clients",
    request_body(content = ProxyAccInfo, description = "Create Proxy Acc"),
    responses(
        (status=200, description= "Create proxy account data successfully!", body=ProxyAccInfo),
        (status=400, description= "Create proxy account data failed", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[post("/proxies")]
pub async fn create_proxy_acc(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<ProxyAccInfo>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let connection_svc = app_state.connection_svc.clone();
    let proxy_acc_info: ProxyAccInfo = body.clone();

    if proxy_acc_info.ip_rotation_period.clone() <= 0 {
        return Ok(ErrorWrapper::builder(
            StatusCode::BAD_REQUEST,
            &format!("cannot create proxy account data, ip_rotation_period must greater than 0"),
        )
        .build());
    }

    if let Some(addr) = proxy_acc_info.whitelisted_ip {
        if let Err(_) = IpAddr::parse_ascii(addr.as_bytes()) {
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                &format!("ip not a address: {}", &addr),
            )
            .build());
        }
    }

    match proxy_acc_info.prioritized_ip.clone() {
        Some(prioritized_ip_request) => {
            if let Err(_) = IpAddr::parse_ascii(prioritized_ip_request.as_bytes()) {
                return Ok(ErrorWrapper::builder(
                    StatusCode::BAD_REQUEST,
                    &format!("prioritized_ip not a address: {}", &prioritized_ip_request),
                )
                .build());
            }
        }
        None => {}
    }

    match connection_svc
        .create_proxy_acc(
            user_addr,
            body.country_geoname_id.into(),
            body.rate_per_kb.into(),
            body.rate_per_second.into(),
            body.whitelisted_ip.clone(),
            body.prioritized_ip.clone(),
            body.prioritized_ip_level.clone(),
            body.ip_rotation_period.clone(),
        )
        .await
    {
        Ok(proxy_acc_data) => Ok(HttpResponse::Ok().json(proxy_acc_data)),
        Err(e) => {
            error!("{}", e);
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                &format!("cannot create proxy account data  err={}", e),
            )
            .build());
        }
    }
}

#[utoipa::path(
    get,
    path = "/clients/proxies",
    tag = "Clients",
    responses(
        (status=200, description= "Get proxy accounts successfully!", body=ProxyAccDto),
        (status=404, description= "Bad request", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/proxies")]
pub async fn get_proxy_acc_data(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_svc = app_state.user_svc.clone();
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    match user_svc.get_client_proxy_accounts(user_addr).await {
        Ok(proxy_acc_dto) => Ok(HttpResponse::Ok().json(proxy_acc_dto)),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::NOT_FOUND,
            &format!("cannot get proxy acc data err={}", e),
        )
        .build()),
    }
}

#[utoipa::path(
    get,
    path = "/clients/overview",
    tag = "Clients",
    responses(
        (status=200, description= "Get client overview successfully!", body=ClientOverview),
        (status=500, description= "Server error", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/overview")]
pub async fn get_client_overview(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_svc = app_state.user_svc.clone();
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    match user_svc.get_client_overview(user_addr).await {
        Ok(client_overview) => Ok(HttpResponse::Ok().json(client_overview)),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::INTERNAL_SERVER_ERROR,
            &format!("cannot get client overview err={}", e),
        )
        .build()),
    }
}

#[utoipa::path(
    get,
    path = "/clients/usage-history",
    tag = "Clients",
    responses(
        (status=200, description= "Get client usage history successfully!", body=[Session]),
        (status=500, description= "Server error", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/usage-history")]
pub async fn get_client_usage_history(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let accounting_svc = app_state.accounting_svc.clone();
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    match accounting_svc.get_usage_history(user_addr).await {
        Ok(client_usage_history) => Ok(HttpResponse::Ok().json(client_usage_history)),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::INTERNAL_SERVER_ERROR,
            &format!("cannot get client usage history err={}", e),
        )
        .build()),
    }
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct HealthCheckResponse {
    pub is_alive: bool,
    pub proxy_acc_exists: bool,
    pub peer_exists: bool,
    pub proxy_acc_id: String,
    pub peer_ip: String,
}

#[utoipa::path(
    get,
    path = "/clients/algo/proxies/health/{id_session}",
    tag = "Algo",
    responses(
        (status=200, description= "Health check peer successful", body=HealthCheckResponse),
        (status=503, description= "Error", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]

#[get("algo/proxies/health/{id_session}")]
pub async fn health_check_peer(
    id_session: web::Path<String>,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    // start time health check
    let start_time = Instant::now();
    let algo_id_session = id_session.into_inner();
    let connection_svc = app_state.connection_svc.clone();
    let bootnode_svc = app_state.bootnode_svc.clone();
    info!("Health check with algo_id_session: {}", algo_id_session);

    // get data from cache
    let cache_key = format!("proxy_health_check:{}", algo_id_session);
    if let Some(cached_data) = app_state.proxy_health_check_cache.get(&cache_key).await {
        info!("Cache hit for proxy_health_check: {}", algo_id_session);
        if cached_data.is_alive {
            return Ok(HttpResponse::Ok().json(cached_data));
        }
        return Ok(HttpResponse::BadRequest().json(cached_data));
    }

    // Check if the peer exists and get its IP address
    let (peer_exists, peer_ip_addr, masternode_ip) = match bootnode_svc.check_peer(algo_id_session.clone()).await {
        Some((peer_ip, masternode_ip)) => (true, peer_ip, masternode_ip),
        None => (false, String::new(), String::new()),
    };



    let _defer = Defer::new(|| {
        let duration = start_time.elapsed();
        if duration.as_secs() > 5 {
            warn!("Health check duration: {:?} , start time: {:?} | End: {:?}", duration, start_time, Utc::now());
        }else{
            info!("Health check duration: {:?} , start time: {:?} | End: {:?}", duration, start_time, Utc::now());
        }
    });


    
    // Check the proxy account health
    match connection_svc.health_check_proxy(&algo_id_session, &masternode_ip).await {
        Ok(proxy_health) => {
            // Combine results and return response
            // Combine results and return response
            // insert data to cache
            let health_check_response = HealthCheckResponse {
                is_alive: proxy_health.proxy_acc_exists && peer_exists,
                proxy_acc_exists: proxy_health.proxy_acc_exists,
                peer_exists: peer_exists,
                proxy_acc_id: proxy_health.proxy_acc_id.unwrap_or_default(),
                peer_ip: peer_ip_addr,
            };
            app_state.proxy_health_check_cache.insert(cache_key, health_check_response.clone()).await;
            Ok(HttpResponse::Ok().json(health_check_response))
        },
        Err(e) => {
            // log for trace
            error!("Cannot perform health check on peer: {}, {}", e, algo_id_session);
            // insert data to cache
            let health_check_response = HealthCheckResponse {
                is_alive: false,
                proxy_acc_exists: false,
                peer_exists: peer_exists,
                proxy_acc_id: String::new(),
                peer_ip: peer_ip_addr,
            };
            app_state.proxy_health_check_cache.insert(cache_key, health_check_response.clone()).await;
            Ok(HttpResponse::BadRequest().json(health_check_response))
        }
    }
}


#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct RefreshProxyReq {
    pub proxy_type: String, // socks5 or http
}

#[derive(Debug, Clone, Deserialize, Serialize, ToSchema)]
pub struct RefreshProxyResp {
    pub proxy_type: String,
    pub ip_v4_address: String,
    pub port: u16,
    pub username: String,
    pub password: String,
}
#[utoipa::path(
    post,
    path = "/clients/refresh-proxy",
    tag = "Clients",
    request_body(content = RefreshProxyReq,description = "Refresh new proxy"),
    responses(
        (status=200, description= "Refresh proxy successfully!", body=AlgoProxyResponse),
        (status=400, description= "Refresh proxy failed", body=ErrorWrapper),
    )
)]
#[post("refresh-proxy")]
pub async fn refresh_proxy(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<RefreshProxyReq>,
) -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().into())
}