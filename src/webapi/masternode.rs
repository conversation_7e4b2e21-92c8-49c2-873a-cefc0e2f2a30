use crate::bootnode::utils::get_geo_from_ip_address;
use crate::monitoring::tracing::TraceContext;
use crate::webapi::AppState;
use actix_web::{get, post, web, HttpRequest, HttpResponse};
use dpn_core::types::api::ErrorWrapper;
use dpn_core::types::geo::{City, Continent, Country, Location};
use dpn_core::types::masternode::AssignMasternodeRes;
use dpn_core::types::{geo::Geo, masternode::MasternodeInfo};
use log::{error, info};
use reqwest::StatusCode;
use serde::Serialize;
use std::sync::Arc;
use utoipa::ToSchema;

pub fn init_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(register_masternode);
    cfg.service(deregister_masternode);
    cfg.service(assign_masternode);
}

#[derive(Serialize, Debug, To<PERSON>che<PERSON>, <PERSON>lone)]
pub struct RegionInfoResponse {
    city_geoname_id: u32,
    country_geoname_id: u32,
}

#[utoipa::path(
    post,
    path = "/masternode/register_masternode",
    tag = "Masternode",
    request_body(content = MasternodeInfo, description = "Register Masternode"),
    responses(
        (status=201, description= "Register masternode successfully!"),
        (status=400, description= "Cannot get masternode public ip", body=ErrorWrapper),
    ),
    security(
        ("x-api-key" = [])
    )
)]
#[post("/register_masternode")]
pub async fn register_masternode(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<MasternodeInfo>,
) -> actix_web::Result<HttpResponse> {
    // Create trace context for this request
    let trace_ctx = TraceContext::from_request(&req, "/register_masternode");
    trace_ctx.log_request_start();

    match req.connection_info().realip_remote_addr() {
        Some(masternode_public_addr) => {
            // Add additional info to trace context
            let trace_ctx = trace_ctx.with_additional_info(&format!("masternode_ip={}", masternode_public_addr));
            trace_ctx.log_debug("Processing masternode registration");

            let masternode_geo;
            match get_geo_from_ip_address(masternode_public_addr.to_string()) {
                Ok(geo) => {
                    masternode_geo = geo;
                    trace_ctx.log_debug(&format!("Geo information retrieved for IP {}", masternode_public_addr));
                },
                Err(e) => {
                    error!("failed to get geo err={}", e);
                    trace_ctx.log_debug(&format!("Failed to get geo information: {}", e));

                    masternode_geo = Geo {
                        city: Some(City {
                            geoname_id: Some(1905598), // VN
                            name: Some("Thành Phố Nha Trang".to_string()),
                        }),
                        continent: Some(Continent {
                            code: Some("AS".to_string()),
                            geoname_id: Some(6255147), // ASIA
                            name: Some("Asia".to_string()),
                        }),
                        country: Some(Country {
                            geoname_id: Some(1562822), // VN
                            is_in_european_union: None,
                            iso_code: Some("VN".to_string()),
                            name: Some("Vietnam".to_string()),
                        }),
                        location: Some(Location {
                            latitude: Some(12.2535),
                            longitude: Some(109.178),
                            metro_code: None,
                            time_zone: Some("Asia/Ho_Chi_Minh".to_string()),
                        }),
                    };
                }
            }

            let mut masternode_info = body.0;
            masternode_info.peer_bind = masternode_info
                .peer_bind
                .replace("0.0.0.0", masternode_public_addr);
            masternode_info.client_bind = masternode_info
                .client_bind
                .replace("0.0.0.0", masternode_public_addr);
            masternode_info.control_bind = masternode_info
                .control_bind
                .replace("0.0.0.0", masternode_public_addr);
            masternode_info.web_bind = masternode_info
                .web_bind
                .replace("0.0.0.0", masternode_public_addr);
            masternode_info.geo = masternode_geo;

            trace_ctx.log_debug("Calling bootnode service to register masternode");

            match app_state
                .bootnode_svc
                .clone()
                .register_masternode(masternode_public_addr.to_string(), masternode_info.clone())
                .await
            {
                Ok(_) => {
                    info!(
                        "registered masternode {} - {:#?}",
                        masternode_public_addr,
                        masternode_info.clone()
                    );
                    trace_ctx.log_request_end("SUCCESS");
                    return Ok(HttpResponse::Ok().into());
                }
                Err(e) => {
                    let error_msg = format!("handle masternode register failed err={}", e);
                    error!("{}", error_msg);
                    trace_ctx.log_debug(&error_msg);
                    trace_ctx.log_request_end("ERROR");
                    return Ok(ErrorWrapper::builder(
                        StatusCode::BAD_REQUEST,
                        &error_msg,
                    )
                    .build());
                }
            };
        }
        None => {
            let error_msg = "cannot get masternode public ip";
            trace_ctx.log_debug(error_msg);
            trace_ctx.log_request_end("ERROR");
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                error_msg,
            )
            .build());
        }
    }
}

#[utoipa::path(
    post,
    path = "/masternode/deregister_masternode",
    tag = "Masternode",
    responses(
        (status=201, description= "Register masternode successfully!"),
        (status=400, description= "Cannot get masternode public ip", body=ErrorWrapper),
    ),
    security(
        ("x-api-key" = [])
    )
)]
#[post("/deregister_masternode")]
pub async fn deregister_masternode(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> actix_web::Result<HttpResponse> {
    // Create trace context for this request
    let trace_ctx = TraceContext::from_request(&req, "/deregister_masternode");
    trace_ctx.log_request_start();

    match req.connection_info().realip_remote_addr() {
        Some(masternode_public_addr) => {
            // Add additional info to trace context
            let trace_ctx = trace_ctx.with_additional_info(&format!("masternode_ip={}", masternode_public_addr));
            trace_ctx.log_debug("Processing masternode deregistration");

            match app_state
                .bootnode_svc
                .clone()
                .deregister_masternode(masternode_public_addr.to_string())
                .await
            {
                Ok(_) => {
                    info!("deregistered masternode ip={}", masternode_public_addr);
                    trace_ctx.log_request_end("SUCCESS");
                },
                Err(e) => {
                    let error_msg = format!("handle masternode deregister failed err={}", e);
                    error!("{}", error_msg);
                    trace_ctx.log_debug(&error_msg);
                    trace_ctx.log_request_end("ERROR");
                },
            };
            return Ok(HttpResponse::Ok().into());
        }
        None => {
            let error_msg = "cannot get masternode public ip";
            trace_ctx.log_debug(error_msg);
            trace_ctx.log_request_end("ERROR");
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                error_msg,
            )
            .build());
        }
    }
}

#[utoipa::path(
    get,
    path = "/connections/assign_masternode",
    tag = "Connection",
    responses(
        (status=200, description= "Get assign masternode successfully", body = AssignMasternodeRes),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/assign_masternode")]
pub async fn assign_masternode(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> actix_web::Result<HttpResponse> {
    // Create trace context for this request
    let trace_ctx = TraceContext::from_request(&req, "/assign_masternode");
    trace_ctx.log_request_start();

    // let continent_code = match req.connection_info().realip_remote_addr() {
    //     Some(peer_ip_addr) => match get_geo_from_ip_address(peer_ip_addr.to_string()) {
    //         Ok(geo) => {
    //             info!("assign_masternode to peer ip={} geo={:#?}", peer_ip_addr , geo);
    //             geo.continent
    //                 .and_then(|c| c.code)
    //                 .unwrap_or(DEFAULT_CONTINENTAL_CODE.to_string())
    //         }
    //         Err(_) => DEFAULT_CONTINENTAL_CODE.to_string(),
    //     },
    //     None => DEFAULT_CONTINENTAL_CODE.to_string(),
    // };

    let geoname_id = match req.connection_info().realip_remote_addr() {
        Some(peer_ip_addr) => {
            // Add additional info to trace context - use clone to avoid moving the original
            let trace_ctx_with_info = trace_ctx.clone().with_additional_info(&format!("client_ip={}", peer_ip_addr));
            trace_ctx_with_info.log_debug("Processing masternode assignment request");

            match get_geo_from_ip_address(peer_ip_addr.to_string()) {
                Ok(geo) => {
                    let id = geo.country.and_then(|c| c.geoname_id).unwrap_or(0);
                    trace_ctx_with_info.log_debug(&format!("Geo information retrieved: country_geoname_id={}", id));
                    id
                },
                Err(e) => {
                    trace_ctx_with_info.log_debug(&format!("Failed to get geo information: {}", e));
                    0
                },
            }
        },
        None => {
            trace_ctx.log_debug("No client IP address found");
            0
        },
    };

    trace_ctx.log_debug(&format!("Assigning masternode for geoname_id={}", geoname_id));

    let rs = app_state
        .bootnode_svc
        .clone()
        .assign_masternode(geoname_id)
        .await;

    trace_ctx.log_request_end("SUCCESS");
    return Ok(HttpResponse::Ok().json(AssignMasternodeRes { masternode: rs }));
}
