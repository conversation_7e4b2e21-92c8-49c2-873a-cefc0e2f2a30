use crate::APP_CONFIG;
use dpn_core::types::msg_queue::{
    BALANCES_EXCHANGE, BALANCES_QUEUE, CONNECTION_EVENTS_ADMIN_QUEUE,
    CONNECTION_EVENTS_EXPLORER_QUEUE, CONNECTION_ROUTING_KEY, DEPOSIT_ROUTING_KEY,
    EVENTS_ACCOUNTNG_QUEUE, EVENTS_EXCHANGE, NOTIFICATION_EXCHANGE, NOTIFICATION_REGISTER_QUEUE,
    NOTIFICATION_REGISTER_ROUTING_KEY, REFERRAL_ROUTING_KEY,
    SESSION_EVENTS_ADMIN_QUEUE, SESSION_EVENTS_EXPLORER_QUEUE, SESSION_EVENTS_NOTIFICATION_QUEUE,
    SESSION_EVENTS_WEBSOCKET_QUEUE, SESSION_ROUTING_KEY, STATS_EXCHANGE, STATS_WEBSOCKET_QUEUE,TXS_EXCHANGE,
    TXS_EXPLORER_QUEUE, TXS_ONCHAIN_QUEUE, WIT<PERSON><PERSON>WALS_EXCHANGE, W<PERSON><PERSON><PERSON>WAL_ROUTING_KEY,
    WITHDRAWALS_EXCHANGE_V2,TXS_ONCHAIN_QUEUE_V2
};
use lapin::{
    options::{ExchangeDeclareOptions, QueueBindOptions, QueueDeclareOptions},
    types::FieldTable,
    Connection, ConnectionProperties,
};
use log::{debug, info};

pub mod consumer;
pub mod publisher;

pub async fn setup_rabbitmq() -> anyhow::Result<(), Box<dyn std::error::Error>> {
    let conn = Connection::connect(&APP_CONFIG.rmq_uri, ConnectionProperties::default())
        .await
        .map_err(|e| anyhow::anyhow!("Failed to connect rabbitmq, err={}", e))?;

    info!("Connected to RabbitMQ");

    let channel = conn
        .create_channel()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to create rabbitmq channel, err={}", e))?;

    info!("Channel created");

    declare_and_bind_queues(&channel).await?;

    Ok(())
}

pub async fn declare_and_bind_queues(
    channel: &lapin::Channel,
) -> anyhow::Result<(), Box<dyn std::error::Error>> {
    declare_exchange(channel, EVENTS_EXCHANGE, lapin::ExchangeKind::Topic).await?;
    declare_and_bind_queue(
        channel,
        CONNECTION_EVENTS_ADMIN_QUEUE,
        EVENTS_EXCHANGE,
        CONNECTION_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        CONNECTION_EVENTS_EXPLORER_QUEUE,
        EVENTS_EXCHANGE,
        DEPOSIT_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        EVENTS_ACCOUNTNG_QUEUE,
        EVENTS_EXCHANGE,
        DEPOSIT_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        EVENTS_ACCOUNTNG_QUEUE,
        EVENTS_EXCHANGE,
        REFERRAL_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        EVENTS_ACCOUNTNG_QUEUE,
        EVENTS_EXCHANGE,
        SESSION_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        EVENTS_ACCOUNTNG_QUEUE,
        EVENTS_EXCHANGE,
        WITHDRAWAL_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        SESSION_EVENTS_ADMIN_QUEUE,
        EVENTS_EXCHANGE,
        SESSION_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        SESSION_EVENTS_EXPLORER_QUEUE,
        EVENTS_EXCHANGE,
        SESSION_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        SESSION_EVENTS_WEBSOCKET_QUEUE,
        EVENTS_EXCHANGE,
        SESSION_ROUTING_KEY,
    )
    .await?;
    declare_and_bind_queue(
        channel,
        SESSION_EVENTS_NOTIFICATION_QUEUE,
        EVENTS_EXCHANGE,
        SESSION_ROUTING_KEY,
    )
    .await?;
    // declare_and_bind_queue(
    //     channel,
    //     TAPPOINT_EVENT_QUEUE,
    //     EVENTS_EXCHANGE,
    //     TAPPOINT_EVENT_ROUTING_KEY,
    // )
    // .await?;

    declare_exchange(channel, STATS_EXCHANGE, lapin::ExchangeKind::Fanout).await?;
    declare_and_bind_queue(channel, STATS_WEBSOCKET_QUEUE, STATS_EXCHANGE, "").await?;

    declare_exchange(channel, TXS_EXCHANGE, lapin::ExchangeKind::Topic).await?;
    declare_and_bind_queue(channel, TXS_EXPLORER_QUEUE, TXS_EXCHANGE, "").await?;

    declare_exchange(channel, WITHDRAWALS_EXCHANGE, lapin::ExchangeKind::Fanout).await?;
    declare_and_bind_queue(channel, TXS_ONCHAIN_QUEUE, WITHDRAWALS_EXCHANGE, "").await?;

    declare_exchange(channel, BALANCES_EXCHANGE, lapin::ExchangeKind::Fanout).await?;
    declare_and_bind_queue(channel, BALANCES_QUEUE, BALANCES_EXCHANGE, "").await?;

    declare_exchange(channel, NOTIFICATION_EXCHANGE, lapin::ExchangeKind::Topic).await?;
    declare_and_bind_queue(
        channel,
        NOTIFICATION_REGISTER_QUEUE,
        NOTIFICATION_EXCHANGE,
        NOTIFICATION_REGISTER_ROUTING_KEY,
    )
    .await?;

    // declare for v2 tx withdrawals
    declare_exchange(channel, WITHDRAWALS_EXCHANGE_V2, lapin::ExchangeKind::Fanout).await?;
    declare_and_bind_queue(channel, TXS_ONCHAIN_QUEUE_V2, WITHDRAWALS_EXCHANGE_V2, "").await?;
    Ok(())
}

pub async fn declare_exchange(
    channel: &lapin::Channel,
    exchange_name: &str,
    kind: lapin::ExchangeKind,
) -> anyhow::Result<()> {
    let exchange_options = ExchangeDeclareOptions {
        durable: true,
        ..ExchangeDeclareOptions::default()
    };
    channel
        .exchange_declare(exchange_name, kind, exchange_options, FieldTable::default())
        .await
        .map_err(|e| {
            anyhow::anyhow!(
                "Failed to create rabbitmq exchange {}, err={}",
                exchange_name,
                e
            )
        })?;
    info!("Created exchange {}", exchange_name);
    Ok(())
}

pub async fn declare_and_bind_queue(
    channel: &lapin::Channel,
    queue_name: &str,
    exchange_name: &str,
    routing_key: &str,
) -> anyhow::Result<()> {
    let queue_options = QueueDeclareOptions {
        durable: true,
        ..QueueDeclareOptions::default()
    };
    channel
        .queue_declare(queue_name, queue_options, FieldTable::default())
        .await
        .map_err(|e| {
            anyhow::anyhow!("Failed to create rabbitmq queue {}, err={}", queue_name, e)
        })?;
    channel
        .queue_bind(
            queue_name,
            exchange_name,
            routing_key,
            QueueBindOptions::default(),
            FieldTable::default(),
        )
        .await
        .map_err(|e| {
            anyhow::anyhow!(
                "Failed to bind queue {} to exchange {}, err={}",
                queue_name,
                exchange_name,
                e
            )
        })?;
    debug!(
        "Bound queue {} to exchange {} with routing key {}",
        queue_name, exchange_name, routing_key
    );
    Ok(())
}
