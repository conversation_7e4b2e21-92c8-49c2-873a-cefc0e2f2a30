use anyhow::{Context as _, Error};
use dpn_core::{
    types::{
        bandwidth::{Session, SessionStatus, UserBandwidthPrice},
        connection::ProxyAccData,
        msg_queue::{SessionCreatedExtra, SessionTerminatedExtra},
    },
    utils::{bytes_to_hex_string, u256_to_szabo},
};
use dpn_db::connection::ConnectionPool;
use dpn_db::model::{
    storage_proxy_acc::StorageProxyAcc, storage_region_info_history::StorageReionInfoHistory,
    storage_session::StorageSession,
};
use ethers::types::{TxHash, U256};
use log::{debug, info};
use sqlx::types::chrono::Utc;
use std::sync::Arc;

const MAX_POOL_SIZE: u32 = 10;

#[derive(Debug, Clone)]
pub struct ConnectionStorage {
    db_conn: ConnectionPool,
}

impl ConnectionStorage {
    pub async fn new(db_url: &str) -> Result<Self, Error> {
        let db_conn = ConnectionPool::builder(db_url, MAX_POOL_SIZE).build().await;
        if let Err(e) = db_conn {
            return Err(Error::msg(format!(
                "cannot construct db conn for accounting storage: error={}",
                e
            )));
        }
        // info!("ConnectionStorage created with pool size {}", MAX_POOL_SIZE);

        Ok(Self {
            db_conn: db_conn.unwrap(),
        })
    }

    pub async fn create_session(
        self: Arc<Self>,
        extra: SessionCreatedExtra,
    ) -> Result<StorageSession, Error> {
        let _self = self.clone();

        let mut ast = _self
            .db_conn
            .access_storage()
            .await
            .map_err(|e| Error::msg(format!("cannot get access storage: {}", e)))?;

        let mut transaction = ast
            .start_transaction()
            .await
            .context("start_transaction()")?;
        let mut session_dal = transaction.session_dal();

        let ephemeral_session = extra.session.clone();
        let session_hash = session_dal
            .create_session(
                ephemeral_session.hash,
                ephemeral_session.client_identifier,
                ephemeral_session.peer_addr,
                ephemeral_session.client_addr,
                ephemeral_session.rate_per_kb_v2 as i64,
                ephemeral_session.rate_per_kb as i64,
                ephemeral_session.handshaked_at,
                ephemeral_session.provider_country_id as i64,
            )
            .await?;
        transaction.commit().await.context("commit()")?;
        Ok(session_hash)
    }

    pub async fn modify_session(
        self: Arc<Self>,
        extra: SessionTerminatedExtra,
        tx_hash: TxHash,
        duration_fee: U256,
        bandwidth_fee: U256,
        total_fee: U256,
        total_fee_v2: Option<U256>,
    ) -> anyhow::Result<StorageSession> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut session_dal = ast.session_dal();
        debug!("modify session to this session.hash {}", extra.session.hash);
        session_dal
            .modify_session(
                extra.session.hash,
                SessionStatus::Finished as i32,
                Some(extra.reason as i32),
                Some(bytes_to_hex_string(tx_hash.as_bytes())),
                Some(extra.session.end_at),
                Some(extra.session.end_at - extra.session.handshaked_at),
                Some(extra.session.bandwidth_usage as i64),
                Some(u256_to_szabo(duration_fee)),
                Some(u256_to_szabo(bandwidth_fee)),
                Some(u256_to_szabo(total_fee)),
                Some(extra.session.provider_country_id as i64),
                Some(u256_to_szabo(total_fee_v2.unwrap())),
            )
            .await
    }

    pub async fn update_bandwidth_usage(
        self: Arc<Self>,
        session_hash: String,
        bandwidth_usage: i64,
    ) -> anyhow::Result<Session> {
        let mut ast = self.db_conn.access_storage().await?;
        let mut session_dal = ast.session_dal();
        let storage_session = session_dal
            .update_bandwidth_usage(session_hash, bandwidth_usage)
            .await?;

        Ok(storage_session.into())
    }

    pub async fn update_user_xp(
        self: Arc<Self>,
        user_addr: String,
        minutes_uptime: f64,
    ) -> anyhow::Result<()> {
        let mut ast = self.db_conn.access_storage().await?;
        let mut user_xp_dal = ast.user_xp_dal();
        user_xp_dal.update_uptime(user_addr, minutes_uptime).await
    }

    pub async fn get_bandwidth_price(
        self: Arc<Self>,
        provider_addr: String,
    ) -> anyhow::Result<UserBandwidthPrice> {
        let mut ast: dpn_db::StorageProcessor<'_> = self.db_conn.access_storage().await?;
        let mut bandwidth_price_dal = ast.bandwidth_price_dal();
        let storage_bandwidth_price_dal = bandwidth_price_dal
            .get_detail_bandwidth_price(provider_addr)
            .await?;
        Ok(storage_bandwidth_price_dal.into())
    }

    pub async fn get_session_by_id(
        self: Arc<Self>,
        session_hash: String,
    ) -> anyhow::Result<Option<Session>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut session_dal = ast.session_dal();
        let storage_session = session_dal.get_session_by_id(session_hash).await?;
        Ok(storage_session.map(|u| u.into()))
    }

    pub async fn get_active_sessions_by_provider(
        self: Arc<Self>,
        provider_addr: String,
    ) -> anyhow::Result<Vec<Session>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut session_dal = ast.session_dal();
        let storage_sessions = session_dal
            .get_active_sessions_by_provider(provider_addr)
            .await?;
        Ok(storage_sessions.iter().map(|u| u.clone().into()).collect())
    }

    pub async fn get_active_sessions(self: Arc<Self>) -> anyhow::Result<Vec<Session>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut session_dal = ast.session_dal();
        let storage_sessions = session_dal.get_active_sessions().await?;
        Ok(storage_sessions.iter().map(|u| u.clone().into()).collect())
    }

    pub async fn get_sessions_by_provider(
        self: Arc<Self>,
        provider_addr: String,
    ) -> anyhow::Result<Vec<Session>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut session_dal = ast.session_dal();
        let storage_sessions = session_dal.get_sessions_by_provider(provider_addr).await?;
        Ok(storage_sessions
            .into_iter()
            .map(|storage_sessions| storage_sessions.into())
            .collect())
    }

    pub async fn get_all_proxy_accounts(self: Arc<Self>) -> anyhow::Result<Vec<StorageProxyAcc>> {
        let _self = self.clone();
        let mut ast: dpn_db::StorageProcessor<'_> = _self.db_conn.access_storage().await?;
        let mut proxy_acc_dal = ast.proxy_acc_dal();
        match proxy_acc_dal.get_all_proxy_accounts().await {
            Ok(proxy_acc_data_storage) => Ok(proxy_acc_data_storage),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn create_proxy_acc(
        self: Arc<Self>,
        proxy_acc_model: ProxyAccData,
    ) -> anyhow::Result<StorageProxyAcc> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut proxy_acc_dal = ast.proxy_acc_dal();
        proxy_acc_dal.create_proxy_acc(proxy_acc_model).await
    }

    pub async fn delete_proxy_acc(
        self: Arc<Self>,
        user_addr: &str,
        proxy_acc_id: &str,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut proxy_acc_dal = ast.proxy_acc_dal();

        match proxy_acc_dal
            .delete_proxy_account(proxy_acc_id.to_string(), user_addr.to_string())
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn get_proxy_acc_by_id(
        self: Arc<Self>,
        proxy_acc_id: String,
    ) -> anyhow::Result<StorageProxyAcc> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut proxy_acc_dal = ast.proxy_acc_dal();
        proxy_acc_dal.get_proxy_acc_by_id(proxy_acc_id).await
    }

    pub async fn get_proxy_acc_by_ip(
        self: Arc<Self>,
        ip: String,
    ) -> anyhow::Result<StorageProxyAcc> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut proxy_acc_dal = ast.proxy_acc_dal();
        proxy_acc_dal.get_proxy_acc_by_ip(ip).await
    }

    pub async fn create_connection_history(
        self: Arc<Self>,
        time_start: i32,
        user_addr: String,
        city_geoname_id: u32,
        country_geoname_id: u32,
        ip_addr: String,
        masternode_id: String,
        login_session_id: String,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut connection_history_dal = ast.connection_history_dal();

        match connection_history_dal
            .create_connection_history(
                time_start,
                user_addr,
                city_geoname_id,
                country_geoname_id,
                ip_addr,
                masternode_id,
                login_session_id,
            )
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn stop_latest_connection_history(
        self: Arc<Self>,
        user_addr: String,
        time_end: i32,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut connection_history_dal = ast.connection_history_dal();

        match connection_history_dal
            .stop_latest_connection_history(user_addr, time_end)
            .await
        {
            Ok(_usr_time_connect) => {
                // TODO(rameight): reenable uptime when db problem is resolved
                // let uptime_duration = usr_time_connect.time_end.unwrap() - usr_time_connect.time_start;
                // match self.clone().update_user_xp(
                //     usr_time_connect.user_addr,
                //     (uptime_duration / 60) as f64
                // ).await {
                //     Ok(_) => Ok(()),
                //     Err(e) => Err(anyhow::anyhow!("{}", e)),
                // }
                Ok(())
            }
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn create_region_info_history(
        self: Arc<Self>,
        region_id: i64,
        is_country: bool,
        name: String,
        country_geoname_id: Option<i64>,
        country_geoname_name: Option<String>,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut region_info_history_dal = ast.region_info_history_dal();

        match region_info_history_dal
            .create_region_info_history(
                region_id,
                is_country,
                name,
                country_geoname_id,
                country_geoname_name,
            )
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn get_all_region_info_history(
        self: Arc<Self>,
    ) -> anyhow::Result<Vec<StorageReionInfoHistory>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut region_info_history_dal = ast.region_info_history_dal();

        match region_info_history_dal.get_all_region_info_history().await {
            Ok(all_region_info_storage) => Ok(all_region_info_storage),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn stop_all_connection_history_of_masternode(self: Arc<Self>) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut connection_history_dal = ast.connection_history_dal();

        match connection_history_dal
            .stop_all_connection_history_of_masternode(Utc::now().timestamp() as i32)
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }
}
