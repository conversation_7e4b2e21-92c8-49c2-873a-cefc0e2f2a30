use crate::webapi::{
    clients::{ClientOverview, ProxyAccUpdate},
    connections::BandwidthPrice,
};
use anyhow::{Context as _, Error};
use dpn_core::types::{
    bandwidth::UserBandwidthPrice,
    connection::ProxyAccData,
    referral::{Referral, ReferralsOverview},
    tier::{TierPoint, UserTier},
    user::User,
};
use dpn_core::utils::u256_to_szabo;
use dpn_db::model::{storage_user::StorageUser, storage_user_online_session::StorageUserOnlineSession};
use dpn_db::{connection::ConnectionPool, proxy_acc_dal::ProxyAccDto};
use dpn_db::model::storage_tx::StorageTx;
use ethers::types::Address;
use log::info;
use sqlx::types::chrono::Utc;
use std::sync::Arc;

const MAX_POOL_SIZE: u32 = 10;


#[derive(Debug, <PERSON>lone)]
pub struct UserStorage {
    db_conn: ConnectionPool,
}

impl UserStorage {
    pub async fn new(db_url: &str) -> Result<Self, Error> {
        let db_conn = ConnectionPool::builder(db_url, MAX_POOL_SIZE).build().await;
        if let Err(e) = db_conn {
            return Err(Error::msg(format!(
                "cannot construct db conn for accounting storage: error={}",
                e
            )));
        }

        // info!("UserStorage created with pool size {}", MAX_POOL_SIZE);
        Ok(Self {
            db_conn: db_conn.unwrap(),
        })
    }

    pub async fn create_user(
        self: Arc<Self>,
        user: StorageUser,
        bandwidth_price: BandwidthPrice,
    ) -> Result<User, Error> {
        let _self = self.clone();
        let ast = _self.db_conn.access_storage().await;
        if let Err(e) = ast {
            return Err(Error::msg(format!("cannot get access storage: {}", e)));
        }
        let mut ast = ast.unwrap();
        let mut transaction = ast
            .start_transaction()
            .await
            .context("start_transaction()")?;
        let mut user_dal = transaction.user_dal();
        let storage_user = user_dal.create_user(user).await?;

        let mut tier_dal = transaction.tier_dal();
        tier_dal
            .create_tier(storage_user.deposit_addr.clone())
            .await?;

        let mut bandwidth_price_dal = transaction.bandwidth_price_dal();
        bandwidth_price_dal
            .create_bandwidth_price(UserBandwidthPrice {
                user_addr: storage_user.deposit_addr.clone(),
                rate_per_kb: bandwidth_price.rate_per_kb,
                rate_per_second: bandwidth_price.rate_per_second,
            })
            .await?;

        let mut referral_dal = transaction.referral_dal();
        referral_dal
            .create_empty_referral(storage_user.deposit_addr.clone())
            .await?;

        let mut user_xp_dal = transaction.user_xp_dal();
        user_xp_dal
            .insert(storage_user.deposit_addr.clone(), 0.0)
            .await?;

        transaction.commit().await.context("commit()")?;
        Ok(storage_user.into())
    }

    pub async fn create_client_user(self: Arc<Self>, user: StorageUser) -> Result<User, Error> {
        let _self = self.clone();
        let ast = _self.db_conn.access_storage().await;
        if let Err(e) = ast {
            return Err(Error::msg(format!("cannot get access storage: {}", e)));
        }
        let mut ast = ast.unwrap();
        let mut user_dal = ast.user_dal();
        let storage_user = user_dal.create_user(user).await?;
        Ok(storage_user.into())
    }

    pub async fn update_user_password(
        self: Arc<Self>,
        email: String,
        salt: String,
        password_hashed: String,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut user_dal = ast.user_dal();
        let user = user_dal.get_user_by_email(email.clone()).await?;

        if let Some(user) = user {
            match user_dal
                .update_password(user.deposit_addr, salt, password_hashed)
                .await
            {
                Ok(_) => Ok(()),
                Err(e) => Err(anyhow::anyhow!("{}", e)),
            }
        } else {
            Err(anyhow::anyhow!("User with email {} not found", email))
        }
    }

    pub async fn get_user_by_id(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Option<User>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut user_dal = ast.user_dal();
        let storage_user = user_dal.get_user_by_addr(user_addr).await?;
        Ok(storage_user.map(|u| u.into()))
    }

    pub async fn get_user_by_pincode(
        self: Arc<Self>,
        pincode: String,
    ) -> anyhow::Result<Option<User>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut user_dal = ast.user_dal();
        let storage_user = user_dal.get_user_by_pincode(pincode).await?;
        Ok(storage_user.map(|u| u.into()))
    }
    

    pub async fn get_tier_by_user_id(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<UserTier> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut tier_dal = ast.tier_dal();
        let storage_user = tier_dal.get_user_tier_by(user_addr).await?;
        Ok(storage_user.into())
    }

    pub async fn get_user_by_email(self: Arc<Self>, email: String) -> anyhow::Result<Option<User>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut user_dal = ast.user_dal();
        let storage_user = user_dal.get_user_by_email(email).await?;
        Ok(storage_user.map(|u| u.into()))
    }

    pub async fn is_email_existed(self: Arc<Self>, email: String) -> anyhow::Result<bool> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut user_dal = ast.user_dal();
        Ok(user_dal.find_email(email).await?)
    }

    pub async fn is_username_existed(self: Arc<Self>, username: String) -> anyhow::Result<bool> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut user_dal = ast.user_dal();
        Ok(user_dal.find_username(username).await?)
    }

    pub async fn create_referral_code(
        self: Arc<Self>,
        user_addr: String,
        referral_code: String,
    ) -> anyhow::Result<String> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut referral_dal = ast.referral_dal();

        match referral_dal
            .create_referral_code(referral_code, user_addr)
            .await
        {
            Ok(_) => Ok(format!("Create referral code success!")),
            Err(err) => Err(anyhow::anyhow!("{}", err)),
        }
    }

    pub async fn import_referral_code(
        self: Arc<Self>,
        user_addr: String,
        friend_referral_code: String,
    ) -> anyhow::Result<(String, String)> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut referral_dal = ast.referral_dal();

        match referral_dal
            .import_referral_code(user_addr, friend_referral_code)
            .await
        {
            Ok((referee_id, referrer_id)) => Ok((referee_id, referrer_id)),
            Err(err) => Err(anyhow::anyhow!("{}", err)),
        }
    }

    pub async fn get_withdrawal_addr_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Option<Address>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut user_dal = ast.user_dal();
        let storage_user = user_dal.get_user_by_addr(user_addr).await?;
        Ok(storage_user.and_then(|u| {
            let user: User = u.into();
            user.withdrawal_addr
        }))
    }

    pub async fn get_referral_of(self: Arc<Self>, user_addr: String) -> anyhow::Result<Referral> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut referral_dal = ast.referral_dal();

        match referral_dal.get_referral_of(user_addr).await {
            Ok(referral_storage) => Ok(referral_storage.into()),
            Err(e) => Err(anyhow::anyhow!("cannot get referral: {}", e)),
        }
    }

    pub async fn get_referrer_addr_by_referral_code(
        self: Arc<Self>,
        referral_code: String,
    ) -> anyhow::Result<String> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut referral_dal = ast.referral_dal();

        match referral_dal
            .get_referrer_addr_by_referral_code(referral_code)
            .await
        {
            Ok(referrer_addr) => Ok(referrer_addr),
            Err(e) => Err(anyhow::anyhow!("cannot get referrer addr: {}", e)),
        }
    }

    pub async fn get_referrals_by(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Vec<Referral>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut referral_dal = ast.referral_dal();

        match referral_dal.get_referrals_by_addr(user_addr).await {
            Ok(referral_storages) => {
                let mut result = Vec::new();
                for referral_storage in referral_storages {
                    result.push(referral_storage.into())
                }
                Ok(result)
            }
            Err(e) => Err(anyhow::anyhow!("cannot get referral: {}", e)),
        }
    }

    pub async fn update_bandwidth_price(
        self: Arc<Self>,
        user_addr: String,
        rate_by_kb: i64,
        rate_by_second: i64,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut bandwidth_price_dal = ast.bandwidth_price_dal();

        match bandwidth_price_dal
            .update_bandwidth_price(user_addr, rate_by_kb, rate_by_second)
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow::anyhow!("cannot update bandwidth price: {}", e)),
        }
    }

    pub async fn get_bandwidth_price_by_user_addr(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<UserBandwidthPrice> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut bandwidth_price_dal = ast.bandwidth_price_dal();
        match bandwidth_price_dal
            .get_detail_bandwidth_price(user_addr)
            .await
        {
            Ok(bandwidth_price) => Ok(bandwidth_price.into()),
            Err(e) => Err(anyhow::anyhow!("cannot get bandwidth price: {}", e)),
        }
    }

    pub async fn get_referrals_overview_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<ReferralsOverview> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut referral_dal = ast.referral_dal();
        match referral_dal.get_referrals_overview_of(user_addr).await {
            Ok(referrals_overview) => Ok(referrals_overview.into()),
            Err(e) => Err(anyhow::anyhow!("cannot get ref overview: {}", e)),
        }
    }

    pub async fn get_total_pending_withdrawal_txs(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<i64> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut tx_dal = ast.tx_dal();
        match tx_dal.get_total_pending_withdrawal_txs_of(user_addr).await {
            Ok(total_pending) => match total_pending {
                Some(total_pending_tx) => return Ok(total_pending_tx),
                None => Err(anyhow::anyhow!(
                    "cannot get total pending withdrawal txs: none result",
                )),
            },
            Err(e) => Err(anyhow::anyhow!(
                "cannot get total pending withdrawal txs: {}",
                e
            )),
        }
    }

    pub async fn get_withdrawals_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Vec<StorageTx>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut tx_dal = ast.tx_dal();
        Ok(tx_dal.get_withdrawal_txs_of(user_addr).await?)
    }

    pub async fn update_withdrawal_addr_of(
        self: Arc<Self>,
        user_addr: String,
        withdrawal_addr: Address,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut user_dal = ast.user_dal();
        Ok(user_dal
            .update_withdrawal_addr_of(user_addr, withdrawal_addr)
            .await?)
    }

    pub async fn update_proxy_account(
        self: Arc<Self>,
        user_addr: String,
        proxy_acc_update: ProxyAccUpdate,
    ) -> anyhow::Result<ProxyAccData> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut proxy_acc_dal = ast.proxy_acc_dal();

        match proxy_acc_dal
            .update_proxy_account(
                user_addr,
                proxy_acc_update.proxy_acc_id,
                proxy_acc_update.ip_rotation_period.into(),
                proxy_acc_update.whitelisted_ip,
                proxy_acc_update.rate_per_kb.into(),
                proxy_acc_update.rate_per_second.into(),
                proxy_acc_update.country_geoname_id.into(),
                proxy_acc_update.city_geoname_id.map(|value| value.into()),
                proxy_acc_update.prioritized_ip,
                proxy_acc_update.prioritized_ip_level,
            )
            .await
        {
            Ok(proxy_acc) => Ok(proxy_acc.into()),
            Err(e) => Err(anyhow::anyhow!("cannot update proxy account data: {}", e)),
        }
    }

    pub async fn delete_proxy_acc(
        self: Arc<Self>,
        user_addr: String,
        proxy_acc_id: String,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut proxy_acc_dal = ast.proxy_acc_dal();

        match proxy_acc_dal
            .delete_proxy_account(proxy_acc_id, user_addr)
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn get_user_tier_points(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Vec<TierPoint>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut tier_dal = ast.tier_dal();

        match tier_dal.get_user_tier_points(user_addr).await {
            Ok(tier_points) => Ok(tier_points.iter().map(|tp| tp.clone().into()).collect()),
            Err(e) => Err(anyhow::anyhow!("cannot get tier points: {}", e)),
        }
    }

    pub async fn add_user_tier_points(
        self: Arc<Self>,
        user_addr: String,
        points: i64,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut tier_dal = ast.tier_dal();
        tier_dal.add_user_tier_points(user_addr, points).await?;
        Ok(())
    }

    pub async fn get_proxy_accounts_by_user_addr(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Vec<ProxyAccDto>> {
        let _self = self.clone();
        let mut ast: dpn_db::StorageProcessor<'_> = _self.db_conn.access_storage().await?;
        let mut proxy_acc_dal = ast.proxy_acc_dal();
        return proxy_acc_dal
            .get_proxy_accounts_by_user_addr(user_addr)
            .await;
    }

    pub async fn get_client_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<ClientOverview> {
        let _self = self.clone();
        let mut ast: dpn_db::StorageProcessor<'_> = _self.db_conn.access_storage().await?;
        let mut _ast: dpn_db::StorageProcessor<'_> = _self.db_conn.access_storage().await?;

        let mut user_dal = ast.user_dal();
        let mut proxy_acc_dal = _ast.proxy_acc_dal();

        match user_dal.get_balance(user_addr.clone()).await {
            Ok(balance) => {
                match proxy_acc_dal
                    .count_proxy_acc_by_addr(user_addr.clone())
                    .await
                {
                    Ok(total_proxies) => Ok(ClientOverview {
                        user_addr,
                        balance: u256_to_szabo(balance),
                        active_proxies: total_proxies.unwrap_or_default(),
                        inactive_proxies: 0,
                    }),
                    Err(e) => Err(anyhow::anyhow!("{}", e)),
                }
            }
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn find_or_create_xp(self: Arc<Self>, user_addr: String) -> anyhow::Result<f64> {
        let _self = self.clone();
        let mut ast: dpn_db::StorageProcessor<'_> = _self.db_conn.access_storage().await?;
        let mut user_xp_dal = ast.user_xp_dal();
        match user_xp_dal.get_uptime(user_addr.clone()).await {
            Ok(uptime) => Ok(uptime),
            Err(e) => {
                // If the user does not have an entry in the user_xps table, create one
                if e.to_string().contains("no rows returned") {
                    user_xp_dal.insert(user_addr, 0.0).await?;
                    Ok(0.0)
                } else {
                    Err(anyhow::anyhow!("{}", e))
                }
            }
        }
    }

    pub async fn update_minutes_uptime(self: Arc<Self>, user_data: Vec<(String, f64)>) -> anyhow::Result<()> {
        let _self = self.clone();

        let mut ast = _self
            .db_conn
            .access_storage()
            .await
            .map_err(|e| Error::msg(format!("cannot get access storage: {}", e)))?;

        let mut transaction = ast
            .start_transaction()
            .await
            .context("start_transaction()")?;
        
        let mut user_xp_dal = transaction.user_xp_dal();

        for (user_addr, minutes_uptime) in user_data {
            match user_xp_dal.get_uptime(user_addr.clone()).await {
                Ok(_) => {
                    user_xp_dal.update_uptime(user_addr, minutes_uptime).await?;
                }
                Err(e) => {
                    if e.to_string().contains("no rows returned") {
                        user_xp_dal.insert(user_addr, minutes_uptime).await?;
                    } else {
                        return Err(anyhow::anyhow!("{}", e));
                    }
                }
            }
        }

        transaction.commit().await.context("commit()")?;
        Ok(())
    }

    pub async fn get_online_sessions_of_user(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Vec<StorageUserOnlineSession>> {
        let _self = self.clone();
        let mut ast: dpn_db::StorageProcessor<'_> = _self.db_conn.access_storage().await?;
        let mut user_online_sessions_dal = ast.user_online_sessions_dal();
        return user_online_sessions_dal
            .get_sessions_by_user(user_addr)
            .await;
    }

    pub async fn store_online_sessions(
        self: Arc<Self>,
        online_ss_redis: Vec<(String, i64, i64)>,
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast = _self
            .db_conn
            .access_storage()
            .await
            .map_err(|e| Error::msg(format!("cannot get access storage: {}", e)))?;
        let mut transaction = ast
            .start_transaction()
            .await
            .context("start_transaction()")?;

        let mut user_online_sessions_dal = transaction.user_online_sessions_dal();

        for (user_addr, started_time, last_updated_at) in online_ss_redis {
            let earned_lp: f64 = (last_updated_at - started_time) as f64 / 60.0;
            let new_online_session = StorageUserOnlineSession {
                user_addr: user_addr.clone(),
                earned_lp: Some(earned_lp),
                start_time: started_time,
                end_time: Some(last_updated_at),
                created_at: Some(Utc::now().timestamp()),
                updated_at: Some(Utc::now().timestamp()),
            };

            _ = user_online_sessions_dal.insert_session(new_online_session).await;
            _ = self.clone().update_minutes_uptime(vec![(user_addr, earned_lp)]).await?;
        }

        transaction.commit().await.context("commit()")?;
        Ok(())
    }

    pub async fn insert_online_session(
        self: Arc<Self>,
        user_addr: String,
        start_time: i64,
        end_time: i64
    ) -> anyhow::Result<()> {
        let _self = self.clone();
        let mut ast: dpn_db::StorageProcessor<'_> = _self.db_conn.access_storage().await?;
        let mut user_online_sessions_dal = ast.user_online_sessions_dal();

        let earned_lp: f64 = (end_time - start_time) as f64 / 60.0;
        let new_online_session = StorageUserOnlineSession {
            user_addr: user_addr.clone(),
            earned_lp: Some(earned_lp),
            start_time,
            end_time: Some(end_time),
            created_at: Some(Utc::now().timestamp()),
            updated_at: Some(Utc::now().timestamp()),
        };
        return user_online_sessions_dal.insert_session(new_online_session).await;
    }
}
